<form action="<?php echo e(route('administration.certificate.generate')); ?>" method="GET" autocomplete="off">
    <div class="card mb-4">
        <div class="card-header header-elements">
            <h5 class="mb-0 text-bold">Generate Certificate</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="mb-3 col-md-12">
                    <label for="user_id" class="form-label">Select Employee <strong class="text-danger">*</strong></label>
                    <select name="user_id" id="user_id" class="select2 form-select <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true" required>
                        <option value="" selected>Select Employee</option>
                        <?php if(isset($employees) && $employees->count() > 0): ?>
                            <?php $__currentLoopData = $employees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($user->id); ?>" <?php echo e((isset($certificate) && $certificate->user->id == $user->id) ? 'selected' : ''); ?>>
                                    <?php echo e($user->name); ?> <?php if($user->employee && $user->employee->alias_name): ?>(<?php echo e($user->employee->alias_name); ?>)<?php endif; ?>
                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <option value="" disabled>No employees found</option>
                        <?php endif; ?>
                    </select>
                    <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                <div class="mb-3 col-md-12">
                    <label for="type" class="form-label">Select Certificate Type <strong class="text-danger">*</strong></label>
                    <select name="type" id="type" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default" required>
                        <option value="" selected disabled>Select Certificate Type</option>
                        <?php $__currentLoopData = certificate_get_types(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $typeKey): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($typeKey); ?>" <?php echo e((isset($certificate) && $certificate->type == $typeKey) ? 'selected' : ''); ?>>
                                <?php echo e(certificate_get_type_config($typeKey)['label']); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                <div class="mb-3 col-md-12">
                    <label class="form-label">Issue Date <strong class="text-danger">*</strong></label>
                    <input type="text" name="issue_date" value="<?php echo e((isset($certificate) ? show_date($certificate->issue_date, 'Y-m-d') : old('issue_date'))); ?>" class="form-control date-picker" placeholder="YYYY-MM-DD" required/>
                    <?php $__errorArgs = ['issue_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="mb-3 col-md-12">
                    <label class="form-label">Joining Date <strong class="text-danger">*</strong></label>
                    <input type="text" name="joining_date" value="<?php echo e((isset($certificate) ? show_date($certificate->joining_date, 'Y-m-d') : old('joining_date'))); ?>" class="form-control date-picker" placeholder="YYYY-MM-DD" required/>
                    <?php $__errorArgs = ['joining_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="mb-3 col-md-12">
                    <label class="form-label">Salary <strong class="text-danger">*</strong></label>
                    <input type="number" name="salary" value="<?php echo e((isset($certificate) ? $certificate->salary : old('salary'))); ?>" class="form-control" placeholder="25000" required/>
                    <?php $__errorArgs = ['salary'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="mb-3 col-md-12">
                    <label class="form-label">Resignation Date <strong class="text-danger">*</strong></label>
                    <input type="text" name="resignation_date" value="<?php echo e((isset($certificate) ? show_date($certificate->resignation_date, 'Y-m-d') : old('resignation_date'))); ?>" class="form-control date-picker" placeholder="YYYY-MM-DD" required/>
                    <?php $__errorArgs = ['resignation_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="mb-3 col-md-12">
                    <label class="form-label">Release Date <strong class="text-danger">*</strong></label>
                    <input type="text" name="release_date" value="<?php echo e((isset($certificate) ? show_date($certificate->release_date, 'Y-m-d') : old('release_date'))); ?>" class="form-control date-picker" placeholder="YYYY-MM-DD" required/>
                    <?php $__errorArgs = ['release_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="mb-3 col-md-12">
                    <label class="form-label">Release Reason <strong class="text-danger">*</strong></label>
                    <input type="text" name="release_reason" value="<?php echo e((isset($certificate) ? $certificate->release_reason : old('release_reason'))); ?>" class="form-control" placeholder="Ex: Health Issue" required/>
                    <?php $__errorArgs = ['release_reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="mb-3 col-md-12">
                    <label class="form-label">Country Name <strong class="text-danger">*</strong></label>
                    <input type="text" name="country_name" value="<?php echo e((isset($certificate) ? $certificate->country_name : old('country_name'))); ?>" class="form-control" placeholder="Ex: United State of America" required/>
                    <?php $__errorArgs = ['country_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="mb-3 col-md-12">
                    <label class="form-label">Visiting Purpose <strong class="text-danger">*</strong></label>
                    <input type="text" name="visiting_purpose" value="<?php echo e((isset($certificate) ? $certificate->visiting_purpose : old('visiting_purpose'))); ?>" class="form-control" placeholder="Ex: United State of America" required/>
                    <?php $__errorArgs = ['visiting_purpose'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="mb-3 col-md-12">
                    <label class="form-label">Leave Starts From <strong class="text-danger">*</strong></label>
                    <input type="text" name="leave_starts_from" value="<?php echo e((isset($certificate) ? show_date($certificate->leave_starts_from, 'Y-m-d') : old('leave_starts_from'))); ?>" class="form-control date-picker" placeholder="YYYY-MM-DD" required/>
                    <?php $__errorArgs = ['leave_starts_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="mb-3 col-md-12">
                    <label class="form-label">Leave Ends On</label>
                    <input type="text" name="leave_ends_on" value="<?php echo e((isset($certificate) ? show_date($certificate->leave_ends_on, 'Y-m-d') : old('leave_ends_on'))); ?>" class="form-control date-picker" placeholder="YYYY-MM-DD"/>
                    <?php $__errorArgs = ['leave_ends_on'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <div class="col-md-12 mt-3">
                <button type="submit" class="btn btn-label-success btn-block">
                    <span class="tf-icon ti ti-progress-check ti-xs me-1"></span>
                    <?php echo e(__('Generate Certificate')); ?>

                </button>
            </div>
        </div>
    </div>
</form>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/certificate/includes/generate_form.blade.php ENDPATH**/ ?>