@extends('administration.certificate.layouts.certificate_main_layout')

@section('certificate_content')
    <div class="certificate-content">
        <h1><u>Experience Letter</u></h1>
        <h3><u>To Whom It May Concern</u></h3>

        <div class="letter-content">
            <p>This is to certify that <strong>{{ $certificate->user->name }}</strong>, {{ $certificate->user->employee->gender == 'Male' ? 'son' : 'daughter' }} of <strong>{{ $certificate->user->employee->father_name ?? 'N/A' }}</strong>, was employed with <strong>{{ config('certificate.company.name') }}</strong> as a <strong>{{ $certificate->user->roles->first()->name ?? 'Employee' }}</strong>.</p>

            <p>{{ $certificate->user->employee->gender == 'Male' ? 'He' : 'She' }} joined our organization on <strong>{{ $certificate->formatted_joining_date }}</strong> and completed {{ $certificate->user->employee->gender == 'Male' ? 'his' : 'her' }} tenure with us on <strong>{{ $certificate->formatted_resignation_date }}</strong>.</p>

            <p>During {{ $certificate->user->employee->gender == 'Male' ? 'his' : 'her' }} employment period with us, {{ $certificate->user->employee->gender == 'Male' ? 'he' : 'she' }} has demonstrated exceptional professional competence, integrity, and dedication to {{ $certificate->user->employee->gender == 'Male' ? 'his' : 'her' }} assigned duties and responsibilities.</p>

            <p>{{ $certificate->user->employee->gender == 'Male' ? 'He' : 'She' }} has consistently maintained high standards of work quality and has been punctual, reliable, and committed throughout {{ $certificate->user->employee->gender == 'Male' ? 'his' : 'her' }} tenure. {{ $certificate->user->employee->gender == 'Male' ? 'His' : 'Her' }} professional conduct and behavior have been exemplary.</p>

            @if($certificate->leave_starts_from)
            <p>{{ $certificate->user->employee->gender == 'Male' ? 'He' : 'She' }} was granted leave starting from <strong>{{ $certificate->formatted_leave_starts_from }}</strong>@if($certificate->leave_ends_on) until <strong>{{ $certificate->formatted_leave_ends_on }}</strong>@endif during {{ $certificate->user->employee->gender == 'Male' ? 'his' : 'her' }} employment period.</p>
            @endif

            <p>We found {{ $certificate->user->employee->gender == 'Male' ? 'him' : 'her' }} to be hardworking, sincere, honest, and dedicated to {{ $certificate->user->employee->gender == 'Male' ? 'his' : 'her' }} responsibilities. {{ $certificate->user->employee->gender == 'Male' ? 'He' : 'She' }} has been a valuable asset to our organization and contributed significantly to our team's success.</p>

            <p>We wish {{ $certificate->user->employee->gender == 'Male' ? 'him' : 'her' }} all the best for {{ $certificate->user->employee->gender == 'Male' ? 'his' : 'her' }} future career and endeavors.</p>

            <p>This experience certificate is issued upon {{ $certificate->user->employee->gender == 'Male' ? 'his' : 'her' }} request for official purposes.</p>

            <p>Issued on <strong>{{ $certificate->formatted_issue_date }}</strong>.</p>
        </div>
    </div>
@endsection
